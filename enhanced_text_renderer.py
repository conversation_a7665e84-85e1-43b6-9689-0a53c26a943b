import asyncio
from PIL import Image, ImageDraw, ImageFont
from astrbot.core.utils.t2i.local_strategy import FontManager, TextMeasurer

class EnhancedMarkdownRenderer:
    """增强的 Markdown 渲染器，提供更清晰的文转图效果"""

    def __init__(
        self,
        font_size: int = 32,  # 增大默认字体
        width: int = 1200,    # 增大默认宽度
        bg_color: tuple = (255, 255, 255),
        text_color: tuple = (0, 0, 0),
        dpi: int = 150,      # 提高 DPI
        antialias: bool = True
    ):
        self.font_size = font_size
        self.width = width
        self.bg_color = bg_color
        self.text_color = text_color
        self.dpi = dpi
        self.antialias = antialias

    async def render_text_to_image(self, text: str) -> Image.Image:
        """将纯文本渲染为高清图片"""
        # 获取字体
        font = FontManager.get_font(self.font_size)

        # 分割文本为行
        lines = TextMeasurer.split_text_to_fit_width(
            text, font, self.width - 40  # 左右边距
        )

        # 计算图片高度
        line_height = self.font_size + 8
        total_height = len(lines) * line_height + 40  # 上下边距

        # 创建高清图片
        image = Image.new(
            "RGB",
            (self.width, total_height),
            self.bg_color
        )

        # 设置 DPI
        image.info['dpi'] = (self.dpi, self.dpi)

        draw = ImageDraw.Draw(image)

        # 如果启用抗锯齿，使用更好的绘制方法
        if self.antialias:
            # 创建临时图像用于抗锯齿处理
            temp_image = Image.new("RGBA", (self.width * 2, total_height * 2), (0, 0, 0, 0))
            temp_draw = ImageDraw.Draw(temp_image)

            # 在高分辨率下绘制
            y = 20
            for line in lines:
                temp_draw.text(
                    (20 * 2, y * 2),
                    line,
                    font=font,
                    fill=(*self.text_color, 255)
                )
                y += line_height

            # 缩放回原始大小，实现抗锯齿效果
            image = temp_image.resize((self.width, total_height), Image.LANCZOS)

        else:
            # 普通绘制
            y = 20
            for line in lines:
                draw.text(
                    (20, y),
                    line,
                    font=font,
                    fill=self.text_color
                )
                y += line_height

        return image

# 使用示例
async def demo_enhanced_render():
    renderer = EnhancedMarkdownRenderer(
        font_size=36,      # 更大的字体
        width=1600,        # 更宽的图片
        dpi=200,          # 更高的分辨率
        antialias=True    # 启用抗锯齿
    )

    text = """这是一个增强的文转图示例

• 支持更大的字体尺寸
• 更高的分辨率 (200 DPI)
• 抗锯齿处理
• 更好的文本清晰度"""

    image = await renderer.render_text_to_image(text)

    # 保存图片
    image.save("enhanced_text_image.png", dpi=(200, 200))
    print("高清文转图已生成: enhanced_text_image.png")

if __name__ == "__main__":
    asyncio.run(demo_enhanced_render())