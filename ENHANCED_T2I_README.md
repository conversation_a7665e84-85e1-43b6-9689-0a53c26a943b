# AstrBot 增强文转图功能

## 概述

本功能通过调整渲染参数，提供更清晰的文字转图片效果。主要改进包括：

- **更大字体尺寸**：默认 36px，提升可读性
- **更高分辨率**：默认 200 DPI，提供更清晰的输出
- **抗锯齿处理**：减少文字边缘锯齿，提升视觉质量
- **可配置参数**：支持自定义字体、颜色、分辨率等

## 核心改进

### 1. 字体和尺寸优化
```python
# 增强前
font_size = 26
width = 800

# 增强后
font_size = 36
width = 1600
dpi = 200
```

### 2. 抗锯齿处理
```python
# 通过双倍渲染 + 缩放实现抗锯齿
temp_image = Image.new("RGBA", (width * 2, height * 2), (0, 0, 0, 0))
# ... 在高分辨率下绘制 ...
image = temp_image.resize((width, height), Image.LANCZOS)
```

### 3. 高DPI支持
```python
# 设置图片DPI信息
image.info['dpi'] = (200, 200)
```

## 使用方法

### 1. 直接使用增强渲染器

```python
from enhanced_text_renderer import EnhancedMarkdownRenderer

async def create_hd_text_image():
    renderer = EnhancedMarkdownRenderer(
        font_size=36,      # 字体大小
        width=1600,        # 图片宽度
        dpi=200,          # 分辨率
        antialias=True    # 抗锯齿
    )

    text = "这是高清文转图示例"
    image = await renderer.render_text_to_image(text)
    image.save("hd_text.png", dpi=(200, 200))
```

### 2. 在插件中使用

将 `enhanced_t2i_plugin.py` 放入 `data/plugins/` 目录，然后：

1. **生成增强文转图**：
   ```
   /增强文转图 您的文本内容
   ```

2. **查看配置**：
   ```
   /文转图配置
   ```

3. **修改配置**：
   编辑插件配置文件，调整以下参数：
   ```json
   {
     "font_size": 36,
     "width": 1600,
     "dpi": 200,
     "antialias": true,
     "bg_color": [255, 255, 255],
     "text_color": [0, 0, 0]
   }
   ```

## 配置参数说明

| 参数 | 默认值 | 说明 | 推荐范围 |
|------|--------|------|----------|
| `font_size` | 36 | 字体大小（像素） | 24-48 |
| `width` | 1600 | 图片宽度（像素） | 800-2000 |
| `dpi` | 200 | 分辨率 | 150-300 |
| `antialias` | true | 是否启用抗锯齿 | true/false |
| `bg_color` | [255,255,255] | 背景色 RGB | [0-255,0-255,0-255] |
| `text_color` | [0,0,0] | 文字色 RGB | [0-255,0-255,0-255] |

## 技术实现

### 抗锯齿算法
1. 创建双倍尺寸的临时图像
2. 在高分辨率下绘制文字
3. 使用 Lanczos 算法缩放回目标尺寸
4. 实现平滑的文字边缘

### 字体优化
1. 自动检测系统可用字体
2. 优先使用高质量字体（如微软雅黑、Arial）
3. 支持中英文混合显示

### 性能考虑
- 抗锯齿会增加内存和CPU使用
- 大尺寸图片需要更多存储空间
- 建议根据使用场景平衡质量和性能

## 故障排除

### 图片模糊
- 增加 `dpi` 值
- 启用 `antialias`
- 增大 `font_size`

### 文字显示不全
- 增加 `width` 值
- 检查文字是否包含特殊字符

### 性能问题
- 降低 `dpi` 和 `width`
- 禁用 `antialias`（质量略降）

## 扩展功能

可以进一步扩展的功能：
- 支持自定义字体文件
- 添加背景图片/渐变
- 支持富文本格式（粗体、斜体等）
- 自动换行优化
- 导出多种格式（PNG、JPEG、WebP）