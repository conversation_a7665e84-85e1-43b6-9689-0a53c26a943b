# What's Changed

> 请仔细阅读：**这是 v4.0.0 的测试版本（beta.4），功能尚未完全稳定和加入**。v4.0.0 被设计为向前兼容，如有任何插件兼容性问题或者其他异常请在 GitHub 提交 [Issue](https://github.com/AstrBotDevs/AstrBot/issues)。在测试版本期间，您可以无缝回退到旧版本的 AstrBot，并且数据不受影响。新版本文档请[从此](https://docs-v4.astrbot.app/)访问，直到第一个 v4.0.0 稳定版本发布。

相较于 beta.4：

1. ‼️修复：新版本在初次保存配置之后，调用 LLM 无法获得响应，但插件指令仍可以使用的问题
2. 修复：部分情况下，Dashboard 内修改配置保存后报错 UnicodeDecodeError
3. 修复：构建 docker 镜像时同时构建 webui，并放入镜像中。
4. 修复：下载 WebUI 文件时，明确版本号，以防止 latest 不一致导致下载的 WebUI 文件版本号与实际所需不符的问题。
5. 优化：优化版本检测，考虑预发布版本，移除 `更新到最新版本` 按钮
6. 优化：增加 abconf_data 缓存，优化性能
7. 优化: 适配 qwen3 的 thinking 类模型
8. 优化: 完善对 rerank model 的可用性检测
9. 新增: 给添加 edge_tts 新增 rate, volume, pitch 参数 ([#2625](https://github.com/Soulter/AstrBot/issues/2625))
