# What's Changed

1. 修复: 修复插件可能存在的无法正常禁用的问题 ([#2352](https://github.com/Soulter/AstrBot/issues/2352))
2. ❗修复：当返回文本为空并且存在函数调用时错误地被终止事件，导致函数调用结果未被正常返回 ([#2491](https://github.com/Soulter/AstrBot/issues/2491))
3. 修复：修复无法清空 AstrBot 配置下的 http_proxy 代理的问题 ([#2434](https://github.com/Soulter/AstrBot/issues/2434))
4. ❗修复：Gemini 下开启流式输出时，持久化的消息结果不完整 ([#2424](https://github.com/Soulter/AstrBot/issues/2424))
5. 修复：注册文件时由于 file:/// 前缀，导致文件被误判为不存在的问题 ([#2325](https://github.com/Soulter/AstrBot/issues/2325))
6. 优化: 为部分类型供应商添加默认的温度选项 ([#2321](https://github.com/Soulter/AstrBot/issues/2321))
7. 优化: 适配 Qwen3 模型非流式输出下需要传入 enable_think 参数（否则报错） ([#2424](https://github.com/Soulter/AstrBot/issues/2424))
8. 优化：支持配置工具调用轮数上限，默认 30
9. 新增: 添加 WebUI 语义化预发布版本提醒和检测功能

> 新版本预告: v4.0.0 即将发布。
