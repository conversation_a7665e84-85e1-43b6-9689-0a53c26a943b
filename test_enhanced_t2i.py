#!/usr/bin/env python3
"""
增强文转图效果测试脚本
用于比较原始效果和增强效果
"""

import asyncio
import os
from PIL import Image, ImageDraw, ImageFont
from enhanced_text_renderer import EnhancedMarkdownRenderer

async def test_comparison():
    """对比测试：原始 vs 增强"""

    test_text = """AstrBot 增强文转图测试

这是用于测试文字转图片清晰度的示例文本。

• 包含中文和英文混合
• 数字符号：1234567890
• 标点符号：，。！？；：
• 特殊字符：@#$%^&*()

原始配置 vs 增强配置对比测试"""

    print("开始生成对比图片...")

    # 1. 原始配置（模拟 AstrBot 默认）
    print("生成原始配置图片...")
    original_renderer = EnhancedMarkdownRenderer(
        font_size=26,      # AstrBot 默认
        width=800,         # AstrBot 默认
        dpi=72,           # PIL 默认
        antialias=False   # 无抗锯齿
    )

    original_image = await original_renderer.render_text_to_image(test_text)
    original_image.save("original_text_image.png", dpi=(72, 72))
    print("✓ 原始配置图片已保存: original_text_image.png")

    # 2. 增强配置
    print("生成增强配置图片...")
    enhanced_renderer = EnhancedMarkdownRenderer(
        font_size=36,      # 增强
        width=1600,        # 增强
        dpi=200,          # 增强
        antialias=True    # 抗锯齿
    )

    enhanced_image = await enhanced_renderer.render_text_to_image(test_text)
    enhanced_image.save("enhanced_text_image.png", dpi=(200, 200))
    print("✓ 增强配置图片已保存: enhanced_text_image.png")

    # 3. 显示文件信息
    print("\n文件对比:")
    orig_size = os.path.getsize("original_text_image.png")
    enh_size = os.path.getsize("enhanced_text_image.png")

    print(f"原始图片: {original_image.size} 像素, {orig_size} 字节")
    print(f"增强图片: {enhanced_image.size} 像素, {enh_size} 字节")
    print(f"文件大小增加: {((enh_size / orig_size - 1) * 100):.1f}%")
    print("\n请打开两个图片文件进行视觉对比。")
    print("增强版本应该有更清晰的文字和更好的可读性。")

async def test_custom_config():
    """测试自定义配置"""

    print("\n测试自定义配置...")

    # 深色主题配置
    dark_renderer = EnhancedMarkdownRenderer(
        font_size=40,
        width=1400,
        dpi=150,
        antialias=True,
        bg_color=(32, 32, 32),      # 深灰背景
        text_color=(255, 255, 255)  # 白字
    )

    dark_text = """深色主题文转图示例

• 深色背景 + 白色文字
• 适合夜间模式
• 提升对比度"""

    dark_image = await dark_renderer.render_text_to_image(dark_text)
    dark_image.save("dark_theme_text_image.png", dpi=(150, 150))
    print("✓ 深色主题图片已保存: dark_theme_text_image.png")

async def benchmark_performance():
    """性能基准测试"""

    print("\n性能基准测试...")

    test_text = "这是性能测试文本。" * 50  # 长文本

    import time

    # 测试原始配置
    start = time.time()
    original_renderer = EnhancedMarkdownRenderer(font_size=26, width=800, antialias=False)
    for _ in range(5):
        await original_renderer.render_text_to_image(test_text)
    original_time = time.time() - start

    # 测试增强配置
    start = time.time()
    enhanced_renderer = EnhancedMarkdownRenderer(font_size=36, width=1600, antialias=True)
    for _ in range(5):
        await enhanced_renderer.render_text_to_image(test_text)
    enhanced_time = time.time() - start

    print(f"原始配置平均时间: {original_time / 5:.3f} 秒")
    print(f"增强配置平均时间: {enhanced_time / 5:.3f} 秒")
    print(f"性能开销: {((enhanced_time / original_time - 1) * 100):.2f}%")
async def main():
    """主测试函数"""
    print("=== AstrBot 增强文转图效果测试 ===\n")

    await test_comparison()
    await test_custom_config()
    await benchmark_performance()

    print("\n=== 测试完成 ===")
    print("生成的文件:")
    for file in ["original_text_image.png", "enhanced_text_image.png", "dark_theme_text_image.png"]:
        if os.path.exists(file):
            print(f"  • {file}")

if __name__ == "__main__":
    asyncio.run(main())