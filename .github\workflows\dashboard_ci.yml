name: AstrBot Dashboard CI

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v5

      - name: npm install, build
        run: |
          cd dashboard
          npm install
          npm run build

      - name: Inject Commit SHA
        id: get_sha
        run: |
          echo "COMMIT_SHA=$(git rev-parse HEAD)" >> $GITHUB_ENV
          mkdir -p dashboard/dist/assets
          echo $COMMIT_SHA > dashboard/dist/assets/version
          cd dashboard
          zip -r dist.zip dist

      - name: Archive production artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist-without-markdown
          path: |
            dashboard/dist
            !dist/**/*.md

      - name: Create GitHub Release
        if: github.event_name == 'push'
        uses: ncipollo/release-action@v1
        with:
          tag: release-${{ github.sha }}
          owner: AstrBotDevs
          repo: astrbot-release-harbour
          body: "Automated release from commit ${{ github.sha }}"
          token: ${{ secrets.ASTRBOT_HARBOUR_TOKEN }}
          artifacts: "dashboard/dist.zip"