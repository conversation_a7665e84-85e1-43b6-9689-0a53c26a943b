# What's Changed

0. ‼️‼️‼️ fix: 修复 4.1.1 版本下，指令调用异常的问题
1. ‼️‼️ fix: 修复多配置文件配置的不同人格无法生效的问题 ([#2739](https://github.com/AstrBotDevs/AstrBot/issues/2739))
2. ‼️‼️ fix: 修复人格所选择的工具无法应用的问题 ([#2739](https://github.com/AstrBotDevs/AstrBot/issues/2739))
3. ‼️‼️ fix: 修复平台配置下的「内容安全」组无法生效 ([#2751](https://github.com/AstrBotDevs/AstrBot/issues/2751))
4. perf: 检查服务提供商可用性时跳过未启用的提供商，解决部分 `provider with id xxx not found` 的问题

fixes: [#2724](https://github.com/AstrBotDevs/AstrBot/issues/2724)
