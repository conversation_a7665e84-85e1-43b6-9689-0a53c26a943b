name: Docker Image CI/CD

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  publish-docker:
    runs-on: ubuntu-latest

    steps:
      - name: Pull The Codes
        uses: actions/checkout@v5
        with:
          fetch-depth: 0  # Must be 0 so we can fetch tags

      - name: Get latest tag (only on manual trigger)
        id: get-latest-tag
        if: github.event_name == 'workflow_dispatch'
        run: |
          tag=$(git describe --tags --abbrev=0)
          echo "latest_tag=$tag" >> $GITHUB_OUTPUT

      - name: Checkout to latest tag (only on manual trigger)
        if: github.event_name == 'workflow_dispatch'
        run: git checkout ${{ steps.get-latest-tag.outputs.latest_tag }}

      - name: Check if version is pre-release
        id: check-prerelease
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            version="${{ steps.get-latest-tag.outputs.latest_tag }}"
          else
            version="${{ github.ref_name }}"
          fi
          if [[ "$version" == *"beta"* ]] || [[ "$version" == *"alpha"* ]]; then
            echo "is_prerelease=true" >> $GITHUB_OUTPUT
            echo "Version $version is a pre-release, will not push latest tag"
          else
            echo "is_prerelease=false" >> $GITHUB_OUTPUT
            echo "Version $version is a stable release, will push latest tag"
          fi

      - name: Build Dashboard
        run: |
          cd dashboard
          npm install
          npm run build
          mkdir -p dist/assets
          echo $(git rev-parse HEAD) > dist/assets/version
          cd ..
          mkdir -p data
          cp -r dashboard/dist data/

      - name: Set QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: Soulter
          password: ${{ secrets.GHCR_GITHUB_TOKEN }}

      - name: Build and Push Docker to DockerHub and Github GHCR
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ steps.check-prerelease.outputs.is_prerelease == 'false' && format('{0}/astrbot:latest', secrets.DOCKER_HUB_USERNAME) || '' }}
            ${{ secrets.DOCKER_HUB_USERNAME }}/astrbot:${{ github.event_name == 'workflow_dispatch' && steps.get-latest-tag.outputs.latest_tag || github.ref_name }}
            ${{ steps.check-prerelease.outputs.is_prerelease == 'false' && 'ghcr.io/soulter/astrbot:latest' || '' }}
            ghcr.io/soulter/astrbot:${{ github.event_name == 'workflow_dispatch' && steps.get-latest-tag.outputs.latest_tag || github.ref_name }}

      - name: Post build notifications
        run: echo "Docker image has been built and pushed successfully"
