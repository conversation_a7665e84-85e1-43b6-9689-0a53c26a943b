# What's Changed

1. 新增：为 FishAudio TTS 添加可选的 reference_id 直接指定功能 ([#2513](https://github.com/AstrBotDevs/AstrBot/issues/2513))
2. 新增：Gemini 添加对 LLMResponse 的 raw_completion 支持
3. 新增：支持官方 QQ 接口发送语音 ([#2525](https://github.com/AstrBotDevs/AstrBot/issues/2525))
4. 新增：调用 deepseek-reasoner 时自动移除 tools ([#2531](https://github.com/AstrBotDevs/AstrBot/issues/2531))
5. 新增：添加 no_proxy 配置支持以优化代理设置 ([#2564](https://github.com/AstrBotDevs/AstrBot/issues/2564))
6. 新增：支持升级的同时更新到指定版本的 WebUI
7. 修复: 修复编辑会话名称窗口的圆角和左右边距问题 ([#2583](https://github.com/AstrBotDevs/AstrBot/issues/2583))
